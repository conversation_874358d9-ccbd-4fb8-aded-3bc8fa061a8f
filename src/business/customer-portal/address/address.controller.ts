import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import {
  ApiCookieAuth,
  ApiCreatedResponse,
  ApiNoContentResponse,
  ApiOkResponse,
  ApiOperation,
  ApiTags,
  ApiForbiddenResponse,
} from '@nestjs/swagger';
import { Mapper } from '@automapper/core';
import { InjectMapper } from '@automapper/nestjs';
import { CreateAddressDto, UpdateAddressDto } from './dto/create-address.dto';
import { AddressDomain } from './domain/address';
import { GetAddressDto, GetAllAddressDto } from './dto/get-address.dto';
import {
  AddressPreferenceDto,
  AddressPreferenceResponseDto,
} from './dto/address-preference.dto';
import { GetAllAddressesDto } from './dto/get-all-addresses.dto';
import { AddressService } from './address.service';
import { JwtContactAuthGuard } from '@core/auth/guards/jwt-contact-auth.guard';
import { CurrentUser } from '@core/auth/decorators/current-user.decorator';
import { JwtPayload } from '@core/auth/domain/auth.types';
import { AddressOperationNotAllowedException } from '@utils/errors/exceptions/address-exceptions';
import { BaseFilterDto } from '@core/infrastructure/filtering/dtos/base-filter.dto';
import { Request } from 'express';
import { SecureFilterService } from '@core/infrastructure/filtering/services/secure-filter.service';
import { ContactsService } from '../../user/contacts/contacts.service';
import { RequireContactPermission } from '../../user/contacts/decorators/require-contact-permission.decorator';
import { ContactPermissionGuard } from '../../user/contacts/guards/contact-permission.guard';
import { ZoneOperationNotAllowedException } from '../../../utils/errors/exceptions/zone-exceptions';
import { ZoneDomain } from '../../zone/zones/domain/zone';
import { GetZoneDto } from '../../zone/zones/dto/get-zone.dto';
import { ZonesService } from '../../zone/zones/zones.service';
import { AddressPreferenceDomain } from './domain/address-preference';

@ApiTags('Customer Portal - Address')
@Controller({
  path: 'customer-portal/address',
  version: '1',
})
@UseGuards(JwtContactAuthGuard, ContactPermissionGuard) // Added ContactPermissionGuard
@RequireContactPermission('address') // Added permission requirement
@ApiCookieAuth('contact_session_token')
@ApiForbiddenResponse({ description: 'Insufficient permissions' })
export class AddressController {
  constructor(
    private readonly addressService: AddressService,
    private readonly contactsService: ContactsService,
    private readonly secureFilterService: SecureFilterService,
    private readonly zonesService: ZonesService,
    @InjectMapper() private readonly mapper: Mapper,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create new Address' })
  @HttpCode(HttpStatus.CREATED)
  @ApiCreatedResponse({ description: 'Created' })
  async create(
    @CurrentUser() contactData: JwtPayload,
    @Body() createAddressDto: CreateAddressDto,
  ): Promise<AddressDomain> {
    try {
      const contact = await this.contactsService.findById(contactData.sub);
      const addressDomain = this.mapper.map(
        createAddressDto,
        CreateAddressDto,
        AddressDomain,
      );
      addressDomain.customerId = contact.userId;
      if (!contact.tenantId) {
        throw new Error('Contact does not have a tenant ID');
      }
      addressDomain.tenantId = contact.tenantId;
      addressDomain.createdBy = contactData.sub; // Assuming contact ID is the creator
      const address = await this.addressService.create(addressDomain);
      const preferenceDomain = new AddressPreferenceDomain();
      preferenceDomain.tenantId = contact.tenantId;
      preferenceDomain.contactId = contact.id;
      preferenceDomain.addressId = address.id;
      preferenceDomain.isDefaultForPickup =
        createAddressDto.isDefaultForPickup || false;
      preferenceDomain.isDefaultForDelivery =
        createAddressDto.isDefaultForDelivery || false;
      preferenceDomain.isFavoriteForPickup =
        createAddressDto.isFavoriteForPickup || false;
      preferenceDomain.isFavoriteForDelivery =
        createAddressDto.isFavoriteForDelivery || false;

      await this.addressService.createAddressPref(preferenceDomain);
      return address;
    } catch (error) {
      throw error;
    }
  }

  @Post(':addressId/duplicate')
  @ApiOperation({ summary: 'Duplicate Address by Address Id' })
  @HttpCode(HttpStatus.CREATED)
  @ApiCreatedResponse({ description: 'Duplicated' })
  async duplicateAddress(
    @CurrentUser() contactData: JwtPayload,
    @Param('addressId') addressId: string,
  ): Promise<AddressDomain> {
    try {
      const address = await this.addressService.getAddressDetails(addressId);
      const contact = await this.contactsService.findById(contactData.sub);
      if (address.customerId !== contact.userId) {
        throw new AddressOperationNotAllowedException(
          contactData.sub,
          'duplicate',
          'Address does not belong to your customer account',
        );
      }
      const duplicatedAddress =
        await this.addressService.duplicateAddress(addressId);
      return duplicatedAddress;
    } catch (error) {
      throw error;
    }
  }

  @Get()
  @ApiOperation({
    summary: 'Get all addresses with pagination and advanced filtering',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetAllAddressDto })
  async getAddressList(
    @CurrentUser() contactData: JwtPayload,
    @Req() request: Request,
    @Query() filter: BaseFilterDto,
  ): Promise<GetAllAddressDto> {
    try {
      const contact = await this.contactsService.findById(contactData.sub);
      const combinedFilter =
        this.secureFilterService.parseKeyOperatorValueQuery(
          request.query,
          filter,
        );
      const result = await this.addressService.getAddressList(
        combinedFilter,
        contact.userId,
        contact.id,
      );
      const mappedData = this.mapper.mapArray(
        result.data,
        AddressDomain,
        GetAddressDto,
      );
      const response: GetAllAddressDto = {
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
        hasNextPage: result.hasNextPage,
        hasPreviousPage: result.hasPreviousPage,
        data: mappedData,
      };
      return response;
    } catch (error) {
      throw error;
    }
  }

  @Get('no-pagination')
  @ApiOperation({
    summary: 'Get all addresses without pagination',
    description: 'Returns all addresses for the customer without pagination',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetAllAddressesDto })
  async getAllAddresses(
    @CurrentUser() contactData: JwtPayload,
  ): Promise<GetAllAddressesDto> {
    try {
      const contact = await this.contactsService.findById(contactData.sub);
      const addresses = await this.addressService.getAllAddresses(
        contact.userId,
        contact.id,
      );

      const mappedData = this.mapper.mapArray(
        addresses,
        AddressDomain,
        GetAddressDto,
      );
      return { data: mappedData };
    } catch (error) {
      throw error;
    }
  }

  @Get(':addressId')
  @ApiOperation({ summary: 'Find address by address Id' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetAddressDto })
  async getAddressDetails(
    @CurrentUser() contactData: JwtPayload,
    @Param('addressId') addressId: string,
  ): Promise<GetAddressDto> {
    try {
      const responseDomain =
        await this.addressService.getAddressDetails(addressId);
      const contact = await this.contactsService.findById(contactData.sub);
      if (responseDomain.customerId !== contact.userId) {
        throw new AddressOperationNotAllowedException(
          contactData.sub,
          'view',
          'Address does not belong to your customer account',
        );
      }

      const preference = await this.addressService.findOneAddressPrefService(
        addressId,
        contact.id,
      );

      const response = this.mapper.map(
        responseDomain,
        AddressDomain,
        GetAddressDto,
      );

      // Attach preference info
      response.isDefaultForPickup = preference?.isDefaultForPickup || false;
      response.isDefaultForDelivery = preference?.isDefaultForDelivery || false;
      response.isFavoriteForPickup = preference?.isFavoriteForPickup || false;
      response.isFavoriteForDelivery =
        preference?.isFavoriteForDelivery || false;
      return response;
    } catch (error) {
      throw error;
    }
  }

  @Put(':addressId')
  @ApiOperation({ summary: 'Update address by address Id' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNoContentResponse({ description: 'No Content' })
  async updateAddressDetails(
    @CurrentUser() contactData: JwtPayload,
    @Param('addressId') addressId: string,
    @Body() updateAddressDto: UpdateAddressDto,
  ): Promise<void> {
    try {
      const existingAddress =
        await this.addressService.getAddressDetails(addressId);
      const contact = await this.contactsService.findById(contactData.sub);
      if (existingAddress.customerId !== contact.userId) {
        throw new AddressOperationNotAllowedException(
          contactData.sub,
          'update',
          'Address does not belong to your customer account',
        );
      }
      const address = this.mapper.map(
        updateAddressDto,
        UpdateAddressDto,
        AddressDomain,
      );
      address.id = addressId;
      address.customerId = contact.userId;
      if (!contact.tenantId) {
        // This check might be redundant
        throw new Error('Contact does not have a tenant ID');
      }
      address.tenantId = contact.tenantId;
      address.updatedBy = contactData.sub; // Assuming contact ID is the updater
      await this.addressService.updateAddressDetails(address);

      const existingPref: any =
        await this.addressService.findOneAddressPrefService(
          addressId,
          contact.id,
        );
      const updatedPref = new AddressPreferenceDomain();
      updatedPref.id = existingPref?.id;
      updatedPref.tenantId = contact.tenantId;
      updatedPref.contactId = contact.id;
      updatedPref.addressId = addressId;
      updatedPref.isDefaultForPickup =
        updateAddressDto.isDefaultForPickup ?? false;
      updatedPref.isDefaultForDelivery =
        updateAddressDto.isDefaultForDelivery ?? false;
      updatedPref.isFavoriteForPickup =
        updateAddressDto.isFavoriteForPickup ?? false;
      updatedPref.isFavoriteForDelivery =
        updateAddressDto.isFavoriteForDelivery ?? false;

      await this.addressService.updateAddressPreference(updatedPref);
      return;
    } catch (error) {
      throw error;
    }
  }

  @Delete(':addressId')
  @ApiOperation({ summary: 'Delete address by address Id' })
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiNoContentResponse({ description: 'No Content' })
  async deleteAddress(
    @CurrentUser() contactData: JwtPayload,
    @Param('addressId') addressId: string,
  ): Promise<void> {
    try {
      const address = await this.addressService.getAddressDetails(addressId);
      const contact = await this.contactsService.findById(contactData.sub);
      if (address.customerId !== contact.userId) {
        throw new AddressOperationNotAllowedException(
          contactData.sub,
          'delete',
          'Address does not belong to your customer account',
        );
      }
      await this.addressService.softDeleteAddress(addressId);
      return;
    } catch (error) {
      throw error;
    }
  }

  @Put(':addressId/preferences')
  @ApiOperation({
    summary: 'Update address preferences',
    description:
      'Set an address as favorite for pickup/delivery or as default for pickup',
  })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: AddressPreferenceResponseDto })
  async updateAddressPreferences(
    @CurrentUser() contactData: JwtPayload,
    @Param('addressId') addressId: string,
    @Body() preferencesDto: AddressPreferenceDto,
  ): Promise<AddressPreferenceResponseDto> {
    try {
      const contact = await this.contactsService.findById(contactData.sub);
      if (!contact.tenantId) {
        throw new Error('Contact does not have a tenant ID');
      }
      const updatedAddress = await this.addressService.updateAddressPreferences(
        addressId,
        contact.id,
        preferencesDto,
        contact.tenantId,
      );

      // Map to response DTO
      return {
        id: updatedAddress.id,
        isFavoriteForPickup: updatedAddress.isFavoriteForPickup,
        isFavoriteForDelivery: updatedAddress.isFavoriteForDelivery,
        isDefaultForPickup: updatedAddress.isDefaultForPickup,
        isDefaultForDelivery: updatedAddress.isDefaultForDelivery,
      };
    } catch (error) {
      throw error;
    }
  }

  @Get('/postalCode/:postalCode')
  @ApiOperation({ summary: 'Find zone by postal code' })
  @HttpCode(HttpStatus.OK)
  @ApiOkResponse({ description: 'OK', type: GetZoneDto })
  async getZoneByPostalCode(
    @CurrentUser() contactData: JwtPayload,
    @Param('postalCode') postalCode: string,
  ): Promise<GetZoneDto> {
    try {
      const contact = await this.contactsService.findById(contactData.sub);
      if (!contact.tenantId) {
        throw new Error('Contact does not have a tenant ID');
      }

      if (!contact.tenantId) {
        throw new ZoneOperationNotAllowedException(
          'unknown',
          'getZoneByPostalCode',
          'Insufficient tenant access permissions',
        );
      }

      const responseDomain = await this.zonesService.getZoneByPostalCode(
        postalCode,
        contact.tenantId,
      );
      const response = this.mapper.map(responseDomain, ZoneDomain, GetZoneDto);
      return response;
    } catch (error) {
      throw error;
    }
  }
}
